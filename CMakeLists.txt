cmake_minimum_required(VERSION 3.16)
project(ftalk_up)

# 프로젝트 루트 경로 설정 (CLion 환경에 맞게 설정)
set(FTALK_UP_ROOT_PATH "/home/<USER>/CLionProjects/ftalk_up" CACHE STRING "Root path for ftalk_up project")
message(STATUS "FTALK_UP_ROOT_PATH: ${FTALK_UP_ROOT_PATH}")

# 컴파일 데이터베이스 생성 (IDE/에디터의 코드 탐색 기능을 위해)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# C++ 표준 설정
set(CMAKE_CXX_STANDARD 98)

# 타겟 이름 충돌 방지를 위한 정책 설정
cmake_policy(SET CMP0002 NEW)

# 서브디렉토리 추가 (순서 중요: 의존성 순서대로)
add_subdirectory(libsrc/libkskyb)
add_subdirectory(daemon_logon_ftk)

# 다른 프로젝트들은 타겟 충돌 문제로 주석 처리
# add_subdirectory(daemon_friendtalk)
# add_subdirectory(command_logon_ftk)
# add_subdirectory(command_friendtalk)
