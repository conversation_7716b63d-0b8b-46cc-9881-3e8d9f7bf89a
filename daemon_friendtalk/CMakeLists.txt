# CMakeLists.txt for daemon_friendtalk
cmake_minimum_required(VERSION 3.10)
project(daemon_friendtalk CXX)

set(CMAKE_CXX_STANDARD 11)

# 디버깅을 위해 경로 출력
message(STATUS "CMAKE_SOURCE_DIR: ${CMAKE_SOURCE_DIR}")
message(STATUS "CMAKE_CURRENT_SOURCE_DIR: ${CMAKE_CURRENT_SOURCE_DIR}")

# Oracle 환경 설정
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# 데이터베이스 접속 정보 설정
# Option 1: 환경변수 사용 (CI/CD 환경에 권장)
# Option 2: 별도 설정 파일 사용 (로컬 개발에 권장)

# 설정 파일이 존재하면 include
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# 환경변수에서 데이터베이스 정보 읽기 (설정 파일보다 우선)
if(DEFINED ENV{SMS_DBSTRING})
    set(SMS_DBSTRING "$ENV{SMS_DBSTRING}")
endif()
if(DEFINED ENV{SMS_DBID})
    set(SMS_DBID "$ENV{SMS_DBID}")
endif()
if(DEFINED ENV{SMS_DBPASS})
    set(SMS_DBPASS "$ENV{SMS_DBPASS}")
endif()
if(DEFINED ENV{MMS_DBSTRING})
    set(MMS_DBSTRING "$ENV{MMS_DBSTRING}")
endif()
if(DEFINED ENV{MMS_DBID})
    set(MMS_DBID "$ENV{MMS_DBID}")
endif()
if(DEFINED ENV{MMS_DBPASS})
    set(MMS_DBPASS "$ENV{MMS_DBPASS}")
endif()

# 빌드 모드 설정 (기본값을 OFF로 변경하여 개발 환경에서 더 안정적으로 빌드)
option(ENABLE_DB_CONNECTION "Enable database connection for Pro*C compilation" ON)

# SQLCHECK 모드 설정 (환경변수로 제어 가능)
if(DEFINED ENV{PROC_SQLCHECK})
    set(PROC_SQLCHECK "$ENV{PROC_SQLCHECK}")
elseif(ENABLE_DB_CONNECTION)
    set(PROC_SQLCHECK "SEMANTICS")
else()
    set(PROC_SQLCHECK "SYNTAX")  # 기본값: SYNTAX (데이터베이스 연결 불필요)
endif()

# 필수 변수 확인 (SEMANTICS 모드일 때만)
if(PROC_SQLCHECK STREQUAL "SEMANTICS")
    if(NOT DEFINED SMS_DBSTRING OR SMS_DBSTRING STREQUAL "" OR SMS_DBSTRING STREQUAL "your_sms_database_string")
        message(FATAL_ERROR "SMS_DBSTRING not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED SMS_DBID OR SMS_DBID STREQUAL "" OR SMS_DBID STREQUAL "your_sms_database_id")
        message(FATAL_ERROR "SMS_DBID not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED SMS_DBPASS OR SMS_DBPASS STREQUAL "" OR SMS_DBPASS STREQUAL "your_sms_database_password")
        message(FATAL_ERROR "SMS_DBPASS not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBSTRING OR MMS_DBSTRING STREQUAL "" OR MMS_DBSTRING STREQUAL "your_mms_database_string")
        message(FATAL_ERROR "MMS_DBSTRING not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBID OR MMS_DBID STREQUAL "" OR MMS_DBID STREQUAL "your_mms_database_id")
        message(FATAL_ERROR "MMS_DBID not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBPASS OR MMS_DBPASS STREQUAL "" OR MMS_DBPASS STREQUAL "your_mms_database_password")
        message(FATAL_ERROR "MMS_DBPASS not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    message(STATUS "SQLCHECK=SEMANTICS mode enabled - database connection required")
else()
    message(STATUS "SQLCHECK=SYNTAX mode enabled - no database connection required")
endif()

# 디버그 정보 (실제 값은 출력하지 않음)
message(STATUS "Database configuration loaded successfully")

# 디렉토리 설정
set(ORG_D ${CMAKE_CURRENT_SOURCE_DIR})
set(BIN_D ${ORG_D}/bin)
set(OBJ_D ${ORG_D}/obj)
set(LIB_D ${ORG_D}/lib)
set(INC_D ${ORG_D}/inc)
set(SRC_D ${ORG_D}/src)

# 인클루드 디렉토리 경로를 조건부로 설정
if(EXISTS "${CMAKE_SOURCE_DIR}/command_friendtalk/inc")
    # 최상위 프로젝트에서 빌드하는 경우
    set(EXT_INC "${CMAKE_SOURCE_DIR}/command_friendtalk/inc")
    message(STATUS "빌드 위치: 최상위 프로젝트")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_friendtalk/inc")
    # 하위 프로젝트에서 빌드하는 경우
    set(EXT_INC "${CMAKE_CURRENT_SOURCE_DIR}/../command_friendtalk/inc")
    message(STATUS "빌드 위치: 하위 프로젝트")
else()
    # CLion 프로젝트 절대 경로 사용 (FTALK_UP_ROOT_PATH 변수 사용)
    if(DEFINED FTALK_UP_ROOT_PATH)
        set(EXT_INC "${FTALK_UP_ROOT_PATH}/command_friendtalk/inc")
    else()
        set(EXT_INC "/home/<USER>/CLionProjects/ftalk_up/command_friendtalk/inc")
    endif()
    message(STATUS "빌드 위치: CLion 절대 경로 사용")
endif()

# 오브젝트 파일 경로를 조건부로 설정
if(EXISTS "${CMAKE_SOURCE_DIR}/command_friendtalk/obj/sms_ctrlsub++.o")
    # 최상위 프로젝트에서 빌드하는 경우
    set(EXT_LIB "${CMAKE_SOURCE_DIR}/command_friendtalk/obj/sms_ctrlsub++.o")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_friendtalk/obj/sms_ctrlsub++.o")
    # 하위 프로젝트에서 빌드하는 경우
    set(EXT_LIB "${CMAKE_CURRENT_SOURCE_DIR}/../command_friendtalk/obj/sms_ctrlsub++.o")
else()
    # CLion 프로젝트 절대 경로 사용 (FTALK_UP_ROOT_PATH 변수 사용)
    if(DEFINED FTALK_UP_ROOT_PATH)
        set(EXT_LIB "${FTALK_UP_ROOT_PATH}/command_friendtalk/obj/sms_ctrlsub++.o")
    else()
        set(EXT_LIB "/home/<USER>/CLionProjects/ftalk_up/command_friendtalk/obj/sms_ctrlsub++.o")
    endif()
    message(STATUS "빌드 위치: CLion 절대 경로 사용 (오브젝트 파일)")
endif()

# 경로 디버그 출력
message(STATUS "EXT_INC: ${EXT_INC}")
message(STATUS "EXT_LIB: ${EXT_LIB}")

# Oracle Pro*C 컴파일러 찾기
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)
if(NOT PROC_EXECUTABLE)
    message(FATAL_ERROR "Oracle Pro*C compiler not found. Please check ORACLE_HOME environment variable.")
endif()

# 컴파일 정의 및 플래그
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# DEBUG 레벨 설정 (개발 시 디버그 코드 활성화)
option(ENABLE_DEBUG "Enable debug output (DEBUG >= 5)" OFF)

# 빌드 타입별 DEBUG 레벨 설정
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug build: DEBUG=5 enabled")
elseif(ENABLE_DEBUG)
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug mode manually enabled (DEBUG=5)")
else()
    add_definitions(-DDEBUG=0)
    message(STATUS "Release build: DEBUG=0 (debug disabled)")
endif()

# 컴파일러 플래그 설정 - 문자열 대신 개별 플래그로 설정
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall")
# 개별 플래그로 분리하여 설정
set(CMMS_FLAGS_LIST
    -g
    -Wall
    -D_MMS_MODE
)

# Oracle Pro*C 전처리 함수 (THREADS 옵션을 조건부로 적용)
function(add_proc_source target_name source_file)
    get_filename_component(source_name ${source_file} NAME_WE)

    set(pc_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.pc)
    set(cpp_file ${CMAKE_CURRENT_BINARY_DIR}/${source_name}.cpp)

    # .cpp를 .pc로 복사
    add_custom_command(
        OUTPUT ${pc_file}
        COMMAND ${CMAKE_COMMAND} -E copy ${source_file} ${pc_file}
        DEPENDS ${source_file}
        COMMENT "Copying ${source_file} to ${pc_file}"
    )

    # DatabaseORA_MMS만 THREADS=YES 사용, 나머지는 제외
    if(source_name STREQUAL "DatabaseORA_MMS")
        set(THREADS_OPTION "THREADS=YES")
        set(CHAR_MAP_OPTION "CHAR_MAP=STRING")
    else()
        set(THREADS_OPTION "")
        set(CHAR_MAP_OPTION "")
    endif()

    # SQLCHECK 옵션과 userid 설정
    if(ENABLE_DB_CONNECTION AND PROC_SQLCHECK STREQUAL "SEMANTICS")
        # 실제 데이터베이스 연결을 사용하는 경우
        set(SQLCHECK_OPTION "SQLCHECK=SEMANTICS")
        set(USERID_OPTION "userid=${MMS_DBID}/${MMS_DBPASS}@${MMS_DBSTRING}")
        set(PARSE_OPTION "PARSE=NONE")  # PARSE=NONE으로 변경하여 헤더 호환성 문제 해결
        set(DEFINE_OPTIONS "define=ORACLE_PROC_COMPILE")
    else()
        # 개발 환경: 데이터베이스 연결 없이 빌드
        if(source_name STREQUAL "DatabaseORA_MMS")
            # DatabaseORA_MMS는 PL/SQL 블록이 있으므로 특별 처리
            # SQLCHECK=NONE으로 설정하여 SQL 검사를 완전히 건너뛰기
            set(SQLCHECK_OPTION "SQLCHECK=NONE")
            set(USERID_OPTION "")
            set(PARSE_OPTION "PARSE=NONE")
            set(DEFINE_OPTIONS "define=BUILD_WITHOUT_DB")
        else()
            set(SQLCHECK_OPTION "SQLCHECK=SYNTAX")
            set(USERID_OPTION "")
            set(PARSE_OPTION "PARSE=NONE")
            set(DEFINE_OPTIONS "")
        endif()
    endif()

    # Oracle Pro*C 전처리
    add_custom_command(
        OUTPUT ${cpp_file}
        COMMAND ${CMAKE_COMMAND} -E env
            "LD_LIBRARY_PATH=${ORACLE_HOME}/lib:$ENV{LD_LIBRARY_PATH}"
            "ORACLE_HOME=${ORACLE_HOME}"
            "TNS_ADMIN=${ORACLE_HOME}/network/admin"
            "PATH=${ORACLE_HOME}/bin:$ENV{PATH}"
            ${PROC_EXECUTABLE}
            MODE=ORACLE
            DBMS=V7
            UNSAFE_NULL=YES
            CHAR_MAP=STRING
            iname=${pc_file}
            include=${INC_D}
            include=${PROC_INCLUDE}
            include=${EXT_INC}
            include=${LIB_D}
            ${THREADS_OPTION}
            CPP_SUFFIX=cpp
            CODE=CPP
            ${PARSE_OPTION}
            CTIMEOUT=1
            MAXOPENCURSORS=10
            HOLD_CURSOR=YES
            RELEASE_CURSOR=YES
            define=__sparc
            ${DEFINE_OPTIONS}
            config=${PROC_CONFIG}
            ${SQLCHECK_OPTION}
            ${USERID_OPTION}
        DEPENDS ${pc_file}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Processing ${pc_file} with Oracle Pro*C"
    )

    target_sources(${target_name} PRIVATE ${cpp_file})
endfunction()

# 인클루드 디렉토리
include_directories(
    ${INC_D}
    ${LIB_D}
    ${PROC_INCLUDE}
    /usr/include/curl
    ${EXT_INC}
)

# 라이브러리 디렉토리
link_directories(
    ${ORACLE_HOME}/lib
    ${ORACLE_HOME}/plsql/lib
    ${ORACLE_HOME}/network/lib
    /usr/lib64
)

# 일반 라이브러리 (Oracle Pro*C 없는 파일들)
add_library(friendtalk_lib STATIC
    ${LIB_D}/Properties.cpp
    ${LIB_D}/myException.cpp
    ${LIB_D}/Curl.cpp
    ${LIB_D}/alimTalkApi.cpp
    ${LIB_D}/jsoncpp.cpp
)

# DatabaseORA_MMS (Oracle Pro*C 필요) - 특별한 컴파일 옵션 적용
add_library(database_ora_mms STATIC)

# 개발 모드에서는 PL/SQL 블록이 없는 개발용 파일 사용
if(ENABLE_DB_CONNECTION)
    add_proc_source(database_ora_mms ${LIB_D}/DatabaseORA_MMS.cpp)
    message(STATUS "Using production DatabaseORA_MMS.cpp with database connection")
else()
    add_proc_source(database_ora_mms ${LIB_D}/DatabaseORA_MMS_dev.cpp)
    message(STATUS "Using development DatabaseORA_MMS_dev.cpp without database connection")
endif()

# 개별 플래그로 전달
target_compile_options(database_ora_mms PRIVATE ${CMMS_FLAGS_LIST})

# 실행파일 - ftkup_send_v1 (Oracle Pro*C 변환 필요 - sql_context 사용)
add_executable(ftkup_send_v1)
add_proc_source(ftkup_send_v1 ${SRC_D}/ftkup_send_v1.cpp)

# 링크 설정
target_link_libraries(ftkup_send_v1
    friendtalk_lib
    database_ora_mms
    ${EXT_LIB}
    clntsh
    curl
    pthread
)

# 출력 디렉토리 설정
set_target_properties(ftkup_send_v1 PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${BIN_D}"
)

# 필요한 경우 아래 주석을 해제하여 추가 타겟을 구성할 수 있습니다
# # friendtalk_mms
# add_executable(friendtalk_mms)
# add_proc_source(friendtalk_mms ${SRC_D}/friendtalk_main.cpp)
# target_link_libraries(friendtalk_mms
#     friendtalk_lib
#     database_ora_mms
#     ${EXT_LIB}
#     clntsh
#     curl
#     pthread
# )
# set_target_properties(friendtalk_mms PROPERTIES
#     RUNTIME_OUTPUT_DIRECTORY "${BIN_D}"
# )
#
# # ftalk_send_v2 
# add_executable(ftalk_send_v2)
# add_proc_source(ftalk_send_v2 ${SRC_D}/ftalk_send_v2.cpp)
# target_link_libraries(ftalk_send_v2
#     friendtalk_lib
#     database_ora_mms
#     ${EXT_LIB}
#     clntsh
#     curl
#     pthread
# )
# set_target_properties(ftalk_send_v2 PROPERTIES
#     RUNTIME_OUTPUT_DIRECTORY "${BIN_D}"
# )
